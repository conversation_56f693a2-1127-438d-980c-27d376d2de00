// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		802BC5812B5DAA71006ACC20 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 802BC5802B5DAA71006ACC20 /* SettingsView.swift */; };
		802BC5832B5DAAC8006ACC20 /* Symbols.swift in Sources */ = {isa = PBXBuildFile; fileRef = 802BC5822B5DAAC8006ACC20 /* Symbols.swift */; };
		802BC5852B5DAB45006ACC20 /* LoginLaunch.swift in Sources */ = {isa = PBXBuildFile; fileRef = 802BC5842B5DAB45006ACC20 /* LoginLaunch.swift */; };
		802BC5872B5DABBA006ACC20 /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 802BC5862B5DABBA006ACC20 /* Constants.swift */; };
		802BC58C2B5DAC74006ACC20 /* Color.swift in Sources */ = {isa = PBXBuildFile; fileRef = 802BC58B2B5DAC74006ACC20 /* Color.swift */; };
		8031BA0D2B4F894000008A07 /* UbaAgentHelperApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8031BA0C2B4F894000008A07 /* UbaAgentHelperApp.swift */; };
		8031BA0F2B4F894000008A07 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8031BA0E2B4F894000008A07 /* ContentView.swift */; };
		8031BA112B4F894000008A07 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8031BA102B4F894000008A07 /* Assets.xcassets */; };
		8031BA142B4F894000008A07 /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 8031BA132B4F894000008A07 /* Preview Assets.xcassets */; };
		80418A252B698A0900C2E2DB /* Preferences.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8046D8ED2B5DF90400A49BBA /* Preferences.swift */; };
		80DDC7732B68C1960049681A /* ServiceManagement.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 80E323592B68BD1400F7145C /* ServiceManagement.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		802BC5802B5DAA71006ACC20 /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		802BC5822B5DAAC8006ACC20 /* Symbols.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Symbols.swift; sourceTree = "<group>"; };
		802BC5842B5DAB45006ACC20 /* LoginLaunch.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LoginLaunch.swift; sourceTree = "<group>"; };
		802BC5862B5DABBA006ACC20 /* Constants.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Constants.swift; sourceTree = "<group>"; };
		802BC58B2B5DAC74006ACC20 /* Color.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Color.swift; sourceTree = "<group>"; };
		8031BA092B4F894000008A07 /* UbaAgentHelper.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = UbaAgentHelper.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8031BA0C2B4F894000008A07 /* UbaAgentHelperApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UbaAgentHelperApp.swift; sourceTree = "<group>"; };
		8031BA0E2B4F894000008A07 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		8031BA102B4F894000008A07 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		8031BA132B4F894000008A07 /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		8031BA152B4F894000008A07 /* UbaAgentHelper.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = UbaAgentHelper.entitlements; sourceTree = "<group>"; };
		8046D8ED2B5DF90400A49BBA /* Preferences.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Preferences.swift; sourceTree = "<group>"; };
		80E323592B68BD1400F7145C /* ServiceManagement.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ServiceManagement.framework; path = System/Library/Frameworks/ServiceManagement.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8031BA062B4F894000008A07 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				80DDC7732B68C1960049681A /* ServiceManagement.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		802BC58A2B5DAC53006ACC20 /* Extensions */ = {
			isa = PBXGroup;
			children = (
				802BC58B2B5DAC74006ACC20 /* Color.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		8031BA002B4F894000008A07 = {
			isa = PBXGroup;
			children = (
				8031BA0B2B4F894000008A07 /* UbaAgentHelper */,
				8031BA0A2B4F894000008A07 /* Products */,
				80E323582B68BD1300F7145C /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		8031BA0A2B4F894000008A07 /* Products */ = {
			isa = PBXGroup;
			children = (
				8031BA092B4F894000008A07 /* UbaAgentHelper.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8031BA0B2B4F894000008A07 /* UbaAgentHelper */ = {
			isa = PBXGroup;
			children = (
				8031BA0C2B4F894000008A07 /* UbaAgentHelperApp.swift */,
				8046D8ED2B5DF90400A49BBA /* Preferences.swift */,
				802BC5842B5DAB45006ACC20 /* LoginLaunch.swift */,
				802BC5822B5DAAC8006ACC20 /* Symbols.swift */,
				8031BA0E2B4F894000008A07 /* ContentView.swift */,
				802BC5802B5DAA71006ACC20 /* SettingsView.swift */,
				802BC5862B5DABBA006ACC20 /* Constants.swift */,
				802BC58A2B5DAC53006ACC20 /* Extensions */,
				8031BA152B4F894000008A07 /* UbaAgentHelper.entitlements */,
				8031BA102B4F894000008A07 /* Assets.xcassets */,
				8031BA122B4F894000008A07 /* Preview Content */,
			);
			path = UbaAgentHelper;
			sourceTree = "<group>";
		};
		8031BA122B4F894000008A07 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				8031BA132B4F894000008A07 /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		80E323582B68BD1300F7145C /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				80E323592B68BD1400F7145C /* ServiceManagement.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8031BA082B4F894000008A07 /* UbaAgentHelper */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8031BA182B4F894000008A07 /* Build configuration list for PBXNativeTarget "UbaAgentHelper" */;
			buildPhases = (
				8031BA052B4F894000008A07 /* Sources */,
				8031BA062B4F894000008A07 /* Frameworks */,
				8031BA072B4F894000008A07 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UbaAgentHelper;
			productName = UbaAgentHelper;
			productReference = 8031BA092B4F894000008A07 /* UbaAgentHelper.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8031BA012B4F894000008A07 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1520;
				LastUpgradeCheck = 1520;
				TargetAttributes = {
					8031BA082B4F894000008A07 = {
						CreatedOnToolsVersion = 15.2;
					};
				};
			};
			buildConfigurationList = 8031BA042B4F894000008A07 /* Build configuration list for PBXProject "UbaAgentHelper" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 8031BA002B4F894000008A07;
			productRefGroup = 8031BA0A2B4F894000008A07 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8031BA082B4F894000008A07 /* UbaAgentHelper */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8031BA072B4F894000008A07 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8031BA142B4F894000008A07 /* Preview Assets.xcassets in Resources */,
				8031BA112B4F894000008A07 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8031BA052B4F894000008A07 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				802BC5852B5DAB45006ACC20 /* LoginLaunch.swift in Sources */,
				802BC5812B5DAA71006ACC20 /* SettingsView.swift in Sources */,
				802BC5832B5DAAC8006ACC20 /* Symbols.swift in Sources */,
				8031BA0F2B4F894000008A07 /* ContentView.swift in Sources */,
				802BC58C2B5DAC74006ACC20 /* Color.swift in Sources */,
				8031BA0D2B4F894000008A07 /* UbaAgentHelperApp.swift in Sources */,
				80418A252B698A0900C2E2DB /* Preferences.swift in Sources */,
				802BC5872B5DABBA006ACC20 /* Constants.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		8031BA162B4F894000008A07 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.2;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		8031BA172B4F894000008A07 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.2;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		8031BA192B4F894000008A07 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = UbaAgentHelper/UbaAgentHelper.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"UbaAgentHelper/Preview Content\"";
				DEVELOPMENT_TEAM = 96DBZ92D3Y;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_LSUIElement = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.epicgames.UbaAgentHelper;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		8031BA1A2B4F894000008A07 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = UbaAgentHelper/UbaAgentHelper.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"UbaAgentHelper/Preview Content\"";
				DEVELOPMENT_TEAM = 96DBZ92D3Y;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INFOPLIST_KEY_LSUIElement = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.epicgames.UbaAgentHelper;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8031BA042B4F894000008A07 /* Build configuration list for PBXProject "UbaAgentHelper" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8031BA162B4F894000008A07 /* Debug */,
				8031BA172B4F894000008A07 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8031BA182B4F894000008A07 /* Build configuration list for PBXNativeTarget "UbaAgentHelper" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8031BA192B4F894000008A07 /* Debug */,
				8031BA1A2B4F894000008A07 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8031BA012B4F894000008A07 /* Project object */;
}
